import React, { useState, useEffect } from "react";
import { Modal, Form, Input, Button, message, Row, Col, InputNumber } from "antd";
import { LogError, LogInfo } from "../../wailsjs/runtime/runtime";
import { SaveGradingCriteriaStruct } from "../../wailsjs/go/main/App";

interface GradingCriteriaDialogProps {
  open: boolean;
  onCancel: () => void;
  onSuccess: (criteria: string) => void;
  initialCriteria: string;
  subject: string;
  structuredCriteria?: {
    defaultCriteria: string;
    scoringPoints: string;
    deductionPoints: string;
    totalScore: number;
  };
}

const GradingCriteriaDialog: React.FC<GradingCriteriaDialogProps> = ({
  open,
  onCancel,
  onSuccess,
  initialCriteria,
  subject,
  structuredCriteria
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  // 初始化表单数据
  useEffect(() => {
    if (open) {
      // 优先使用结构化数据
      if (structuredCriteria && (structuredCriteria.defaultCriteria || structuredCriteria.scoringPoints || structuredCriteria.deductionPoints)) {
        form.setFieldsValue({
          criteria: structuredCriteria.defaultCriteria,
          scoringPoints: structuredCriteria.scoringPoints,
          deductionPoints: structuredCriteria.deductionPoints,
          totalScore: structuredCriteria.totalScore,
        });
        LogInfo("使用结构化评分标准初始化表单");
      } else {
        // 回退到解析文本格式
        const parts = initialCriteria.split('\n\n');
        // 默认值
        let mainCriteria = initialCriteria;
        let scoringPoints = '';
        let deductionPoints = '';
        // 检查是否有得分点或不得分点的标记
        let scoringPointsIndex = -1;
        let deductionPointsIndex = -1;
        // 在所有部分中查找得分点和不得分点的标记
        for (let i = 0; i < parts.length; i++) {
          if (parts[i].startsWith('得分点：')) {
            scoringPointsIndex = i;
          } else if (parts[i].startsWith('不得分点：')) {
            deductionPointsIndex = i;
          }
        }
        // 如果找到了得分点或不得分点的标记
        if (scoringPointsIndex >= 0 || deductionPointsIndex >= 0) {
          // 主要评分标准是从开始到第一个标记之前的所有部分
          const firstMarkerIndex = Math.min(
            scoringPointsIndex >= 0 ? scoringPointsIndex : parts.length,
            deductionPointsIndex >= 0 ? deductionPointsIndex : parts.length
          );
          // 合并主要评分标准的所有部分
          mainCriteria = parts.slice(0, firstMarkerIndex).join('\n\n');
          // 如果找到了得分点标记
          if (scoringPointsIndex >= 0) {
            scoringPoints = parts[scoringPointsIndex].substring(4); // 移除前缀
          }
          // 如果找到了不得分点标记
          if (deductionPointsIndex >= 0) {
            deductionPoints = parts[deductionPointsIndex].substring(5); // 移除前缀
          }
        } else if (parts.length >= 3) {
          // 兼容旧的解析方式，假设前三个部分分别是评分标准、得分点和不得分点
          mainCriteria = parts[0];

          // 处理得分点，移除前缀
          scoringPoints = parts[1];
          if (scoringPoints.startsWith('得分点：')) {
            scoringPoints = scoringPoints.substring(4);
          }

          // 处理不得分点，移除前缀
          deductionPoints = parts[2];
          if (deductionPoints.startsWith('不得分点：')) {
            deductionPoints = deductionPoints.substring(5);
          }
        }

        form.setFieldsValue({
          criteria: mainCriteria,
          scoringPoints: scoringPoints,
          deductionPoints: deductionPoints,
          totalScore: 0, // 默认总分
        });
        LogInfo("使用文本格式评分标准初始化表单");
      }
    }
  }, [open, initialCriteria, structuredCriteria, form]);

  const handleSubmit = async () => {
    try {
      setLoading(true);
      const values = await form.validateFields();

      // 获取表单值
      const defaultCriteria = values.criteria || '';
      const scoringPoints = values.scoringPoints || '';
      const deductionPoints = values.deductionPoints || '';
      const totalScore = values.totalScore || 0;

      // 使用新的结构化保存方法
      await SaveGradingCriteriaStruct(
        defaultCriteria,
        scoringPoints,
        deductionPoints,
        totalScore,
        subject
      );
      LogInfo("保存结构化评分标准到配置文件");

      message.success("评分标准保存成功");

      // 为了保持向后兼容，仍然传递合并后的文本给父组件
      const contentParts = [defaultCriteria];
      if (scoringPoints) {
        contentParts.push(`得分点：${scoringPoints}`);
      }
      if (deductionPoints) {
        contentParts.push(`不得分点：${deductionPoints}`);
      }
      const combinedCriteria = contentParts.join('\n\n');
      onSuccess(combinedCriteria);
    } catch (err) {
      LogError(`保存评分标准失败: ${err}`);
      message.error("保存失败: " + (err as Error).message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      title="设置评分标准"
      open={open}
      onCancel={onCancel}
      width={570}
      // styles={{ body: { height: '480px', overflowY: 'auto' } }}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          取消
        </Button>,
        <Button
          key="submit"
          type="primary"
          loading={loading}
          onClick={handleSubmit}
        >
          保存
        </Button>,
      ]}
    >
      <Form form={form} layout="vertical">
        <Form.Item
          name="criteria"
          label="评分标准"
          rules={[{ required: true, message: '请输入评分标准' }]}
        >
          <Input.TextArea
            showCount
            placeholder="请输入评分内容，详细描述问题和评分标准，这和评分准确率成正相关。"
            style={{ height: '160px', resize: 'none' }}
          />
        </Form.Item>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="scoringPoints"
              label="补充得分点答案"
            >
              <Input.TextArea
                showCount
                placeholder="请输入得分点"
                style={{ height: '160px', resize: 'none' }}
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="deductionPoints"
              label="不得分点答案"
            >
              <Input.TextArea
                showCount
                placeholder="请输入不得分点"
                style={{ height: '160px', resize: 'none' }}
              />
            </Form.Item>
          </Col>
        </Row>

        <Form.Item
          name="totalScore"
          label="总分"
          rules={[{ required: true, message: '请输入总分' }]}
        >
          <InputNumber
            min={0}
            max={1000}
            placeholder="请输入总分"
            style={{ width: '100%' }}
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default GradingCriteriaDialog;
