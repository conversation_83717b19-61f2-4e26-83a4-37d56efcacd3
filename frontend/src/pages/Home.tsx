import React, { useState, useEffect, useCallback } from "react";
import { Button, Layout, Select, Image, message, Empty, InputNumber, Card, FloatButton, Spin, Modal } from "antd";
import { useLoading } from "../contexts/LoadingContext";
import { useAuth } from "../contexts/AuthContext";
import { WalletOutlined, UserOutlined, LogoutOutlined, AimOutlined } from "@ant-design/icons";
import { useNavigate } from "react-router-dom";
import LogoImage from "../assets/images/appicon.png";
import {
  OpenURL,
  FetchImage,
  FillGrade,
  SaveGradingCriteria,
  GetGradingCriteria,
  GetGradingCriteriaStruct,
  GradeWithStruct,
  AutoGradingLoop,
  AutoGradingLoopCustom,
  ScreenshotArea,
  ExecuteElementOperations,
  SaveSelectedUrl,
  GetSelectedUrl,
} from "../../wailsjs/go/main/App";
import { Grade } from "../../wailsjs/go/main/APIService";
// 移除 AuthDialog 导入
import GradingCriteriaDialog from "../components/GradingCriteriaDialog";
import PageAreaSelector from "../components/PageAreaSelector";
import ElementSelector, { ElementInfo } from "../components/ElementSelector";

import GradingRecordsDialog from "../components/GradingRecordsDialog";

import "./Home.css";
import Sider from "antd/es/layout/Sider";
import {
  GlobalOutlined,
  CameraOutlined,
  SendOutlined,
  ThunderboltOutlined,
  UploadOutlined,
  PushpinOutlined,
  PushpinFilled,
  EditOutlined,
  ScissorOutlined,
} from "@ant-design/icons";
import { LogInfo, LogError, WindowSetAlwaysOnTop } from "../../wailsjs/runtime/runtime";
import { main } from "../../wailsjs/go/models";
import { debounce } from "../utils/debounce";

declare module "../../wailsjs/go/main/App" {
  export function FetchImage(url: string): Promise<string>;
  export function SaveGradingCriteria(criteria: string, subject: string): Promise<void>;
  export function GetGradingCriteria(): Promise<{ criteria: string; subject: string }>;
  export function ScreenshotArea(x: number, y: number, width: number, height: number, maxWidth: number): Promise<string>;
  export function CaptureFullPage(): Promise<string>;
  export function ExecuteElementOperations(operationsJson: string): Promise<void>;
  export function AutoGradingLoopCustom(subject: string, prompt: string, count: number, areaCoords: {x: number, y: number, width: number, height: number}, elementsJson: string): Promise<void>;
  export function SaveSelectedUrl(url: string): Promise<void>;
  export function GetSelectedUrl(): Promise<string>;
}

const { Header, Content, Footer } = Layout;

const Home: React.FC = () => {
  const [selectedUrl, setSelectedUrl] = useState<string>("");
  const [urlOptions, setUrlOptions] = useState<{ label: string; value: string }[]>([]);
  const [imageData, setImageData] = useState<string>("");
  // 对话框状态
  const [gradingCriteriaDialogOpen, setGradingCriteriaDialogOpen] = useState(false);
  const [gradingRecordsDialogVisible, setGradingRecordsDialogVisible] = useState(false);
  const [textAreaValue, setTextAreaValue] = useState<string>("");
  const [gradingResults, setGradingResults] = useState<main.GradingResponse | null>(null);
  const [selectedSubject, setSelectedSubject] = useState<string>("geography");

  // 结构化评分标准状态
  const [structuredCriteria, setStructuredCriteria] = useState<{
    defaultCriteria: string;
    scoringPoints: string;
    deductionPoints: string;
    totalScore: number;
  }>({
    defaultCriteria: "",
    scoringPoints: "",
    deductionPoints: "",
    totalScore: 0,
  });
  const { setLoading } = useLoading();
  const { username, balance, isBalanceSufficient, logout, refreshBalance, updateBalance } = useAuth();
  const navigate = useNavigate();

  // 区域截图相关状态
  const [isPageAreaSelectorVisible, setIsPageAreaSelectorVisible] = useState(false);
  const [selectedAreaCoords, setSelectedAreaCoords] = useState<{x: number, y: number, width: number, height: number} | null>(null);

  // 元素选择器相关状态
  const [isElementSelectorVisible, setIsElementSelectorVisible] = useState(false);
  const [selectedElements, setSelectedElements] = useState<ElementInfo[]>([]);

  // 评分结果是否禁用
  const [scoreInputDisabled, setscoreInputDisabled] = useState(true);
  // 自定义分数
  const [customScore, setCustomScore] = useState<number | null>(null);
  // 窗口置顶状态，默认为 true
  const [isAlwaysOnTop, setIsAlwaysOnTop] = useState(false);
  // 自动阅卷相关状态
  const [paperCount, setPaperCount] = useState<number>();
  const [isAutoGrading, setIsAutoGrading] = useState<boolean>(false);
  const [isWaitingForAPI, setIsWaitingForAPI] = useState<boolean>(false);
  const [autoGradingProgress, setAutoGradingProgress] = useState<{ current: number; total: number }>({ current: 0, total: 0 });
  // 自动阅卷按钮禁用状态和错误提示
  const [autoGradingDisabled, setAutoGradingDisabled] = useState<boolean>(true);
  const [autoGradingErrorMsg, setAutoGradingErrorMsg] = useState<string>("");

  const toggleScoreInput = () => {
    if (scoreInputDisabled) {
      // 切换回禁用模式，更新评分结果
      setCustomScore(gradingResults?.score || 0);
    } else {
      // 切换到编辑模式，初始化自定义分数为当前分数
      if (gradingResults && customScore !== null) {
        // 使用createFrom方法创建一个新的GradingResponse对象
        const updatedResults = main.GradingResponse.createFrom(gradingResults);
        // 更新score值
        updatedResults.score = customScore;
        setGradingResults(updatedResults);
      }
    }
    setscoreInputDisabled(!scoreInputDisabled);
  };

  // 切换窗口置顶
  const toggleAlwaysOnTop = () => {
    const newState = !isAlwaysOnTop;
    WindowSetAlwaysOnTop(newState);
    setIsAlwaysOnTop(newState);
    message.info(newState ? "窗口已置顶" : "取消窗口置顶");
  };

  // 科目选项
  const [subjectOptions, setSubjectOptions] = useState<{ label: string; value: string }[]>([]);

  // 使用debounce函数创建一个延迟保存的函数
  // 10秒的延迟，减少保存频率
  const debouncedSaveGradingCriteria = useCallback(
    debounce((criteria: string, subject: string) => {
      LogInfo("延迟保存评分标准到配置文件");
      SaveGradingCriteria(criteria, subject).catch((err) => {
        LogError(`保存评分标准失败: ${err}`);
      });
    }, 10000),
    []
  );

  const handleTextAreaSubmit = async () => {
    LogInfo("handleTextAreaSubmit called");
    // 检查是否有抓图数据和评分标准
    if (!imageData) {
      message.warning("请先抓图再进行评分");
      return;
    }

    if (!textAreaValue.trim()) {
      message.warning("请输入评分标准");
      return;
    }

    setLoading(true);
    // 添加整体计时
    const startTime = performance.now();
    try {
      // 调用APIService.Grade函数，传入抓图数据和评分标准
      LogInfo(`使用科目: ${selectedSubject}`);

      // 添加API调用计时
      const apiStartTime = performance.now();

      // 优先使用结构化评分标准进行评分
      let results;
      if (structuredCriteria.defaultCriteria || structuredCriteria.scoringPoints || structuredCriteria.deductionPoints) {
        LogInfo("使用结构化评分标准进行评分");
        results = await GradeWithStruct(
          imageData,
          structuredCriteria.defaultCriteria,
          structuredCriteria.scoringPoints,
          structuredCriteria.deductionPoints,
          structuredCriteria.totalScore,
          selectedSubject
        );
      } else {
        LogInfo("使用传统评分标准进行评分");
        results = await Grade(imageData, textAreaValue, selectedSubject);
      }

      const apiEndTime = performance.now();
      const apiDuration = apiEndTime - apiStartTime;
      LogInfo(`API调用耗时: ${apiDuration.toFixed(2)}ms`);

      // 如果返回了新的余额，更新缓存
      if (results && results.balance !== undefined) {
        updateBalance(results.balance);
      }

      // 添加结果处理计时
      const processStartTime = performance.now();
      message.success("评分请求已发送");
      // 使用新的GradingResponse结构
      setGradingResults(results);
      const processEndTime = performance.now();
      const processDuration = processEndTime - processStartTime;
      LogInfo(`结果处理耗时: ${processDuration.toFixed(2)}ms`);

      LogInfo(`评分结果: 得分=${results.score}, 详情=${results.grading_details}`);
    } catch (err) {
      LogError(`评分失败: ${err}`);
      const errorMessage = err instanceof Error ? err.message : String(err);

      // 处理特殊错误码
      if (errorMessage.includes("401")) {
        message.error("登录已过期，请重新登录");
        logout();
        navigate("/login", { replace: true });
      } else if (errorMessage.includes("402")) {
        message.error("账户余额不足，请充值后再试");
        // 强制刷新余额
        refreshBalance();
      } else {
        message.error(`评分失败: ${errorMessage}`);
      }
    } finally {
      const endTime = performance.now();
      const totalDuration = endTime - startTime;
      LogInfo(`评分总耗时: ${totalDuration.toFixed(2)}ms`);
      setLoading(false);
    }
  };

  // 加载保存的评分标准
  useEffect(() => {
    const loadSavedSettings = async () => {
      try {
        // 优先尝试加载结构化评分标准
        try {
          const structuredData = await GetGradingCriteriaStruct();
          if (structuredData) {
            // 设置结构化数据
            setStructuredCriteria({
              defaultCriteria: structuredData.default_criteria || "",
              scoringPoints: structuredData.scoring_points || "",
              deductionPoints: structuredData.deduction_points || "",
              totalScore: structuredData.total_score || 0,
            });

            // 为了向后兼容，也设置合并后的文本
            const parts = [];
            if (structuredData.default_criteria) {
              parts.push(structuredData.default_criteria);
            }
            if (structuredData.scoring_points) {
              parts.push(`得分点:\n${structuredData.scoring_points}`);
            }
            if (structuredData.deduction_points) {
              parts.push(`不得分点:\n${structuredData.deduction_points}`);
            }
            if (structuredData.total_score) {
              parts.push(`总分：${structuredData.total_score}`);
            }
            const combinedText = parts.join('\n\n');
            setTextAreaValue(combinedText);

            if (structuredData.subject) {
              setSelectedSubject(structuredData.subject);
              LogInfo(`加载保存的科目: ${structuredData.subject}`);
            }
            LogInfo("加载保存的结构化评分标准");
          }
        } catch (structError) {
          LogInfo("结构化评分标准不存在，尝试加载传统格式");
          // 如果结构化数据不存在，回退到传统格式
          const savedCriteria = await GetGradingCriteria();
          if (savedCriteria) {
            if (savedCriteria.criteria) {
              setTextAreaValue(savedCriteria.criteria);
              LogInfo("加载保存的传统评分标准");
            }
            if (savedCriteria.subject) {
              setSelectedSubject(savedCriteria.subject);
              LogInfo(`加载保存的科目: ${savedCriteria.subject}`);
            }
          }
        }
      } catch (error) {
        LogError(`加载保存的评分标准失败: ${error}`);
      }
    };

    loadSavedSettings();

    // 组件加载时设置窗口置顶
    WindowSetAlwaysOnTop(false);
  }, []);

  // 监听保存评分标准事件（应用退出时触发）
  useEffect(() => {
    const handleSaveGradingCriteria = () => {
      try {
        SaveGradingCriteria(textAreaValue, selectedSubject);
        LogInfo("应用退出时保存评分标准");
      } catch (error) {
        LogError(`保存评分标准失败: ${error}`);
      }
    };

    window.runtime.EventsOn("saveGradingCriteria", handleSaveGradingCriteria);

    return () => {
      window.runtime.EventsOff("saveGradingCriteria", handleSaveGradingCriteria);
    };
  }, [textAreaValue, selectedSubject]);



  // 监听阅卷记录对话框事件
  useEffect(() => {
    const handleShowGradingRecords = () => {
      LogInfo("显示阅卷记录列表");
      setGradingRecordsDialogVisible(true);
    };

    // 注册事件监听器
    window.runtime.EventsOn("show:gradingRecords", handleShowGradingRecords);

    return () => {
      window.runtime.EventsOff("show:gradingRecords", handleShowGradingRecords);
    };
  }, []);



  // 监听导出成功事件
  useEffect(() => {
    const handleExportSuccess = (filePath: string, exportDir: string) => {
      LogInfo(`导出成功，文件路径: ${filePath}`);
      message.success(`导出成功，文件保存在: ${exportDir}`);
    };

    const handleExportNoRecords = () => {
      LogInfo("没有记录可导出");
      message.warning("没有阅卷记录可导出");
    };

    // 注册事件监听器
    window.runtime.EventsOn("export:success", function () {
      // @ts-ignore
      const filePath = arguments[0];
      // @ts-ignore
      const exportDir = arguments[1];
      handleExportSuccess(filePath, exportDir);
    });
    window.runtime.EventsOn("export:noRecords", handleExportNoRecords);

    return () => {
      window.runtime.EventsOff("export:success");
      window.runtime.EventsOff("export:noRecords");
    };
  }, []);

  // 在每次评分完成后刷新余额
  useEffect(() => {
    if (gradingResults) {
      refreshBalance();
    }
  }, [gradingResults, refreshBalance]);

  // 当余额不足时显示警告
  useEffect(() => {
    if (balance !== null && balance < 100 && isBalanceSufficient === false) {
      message.warning("账户余额不足，部分功能已禁用，请充值后继续使用", 5);
    }
  }, [balance, isBalanceSufficient]);

  // 当selectedUrl变化时保存到配置
  useEffect(() => {
    // 确保selectedUrl有值且不是初始化阶段
    if (selectedUrl) {
      LogInfo(`保存URL到配置: ${selectedUrl}`);
      SaveSelectedUrl(selectedUrl).catch(err => {
        LogError(`保存URL失败: ${err}`);
      });
    }
  }, [selectedUrl]);

  // 初始化时验证自动阅卷条件
  useEffect(() => {
    validateAutoGradingRequirements();
  }, [selectedUrl, selectedAreaCoords, selectedElements, textAreaValue]);

  // 监听自动阅卷相关事件
  useEffect(() => {
    // 自动阅卷开始
    const handleAutoGradingStart = () => {
      LogInfo("自动阅卷开始");
      setIsAutoGrading(true);
      message.info("自动阅卷已开始");
    };

    // 自动阅卷停止
    const handleAutoGradingStop = () => {
      LogInfo("自动阅卷停止");
      setIsAutoGrading(false);
      message.info("自动阅卷已停止");
    };

    // 自动阅卷完成
    const handleAutoGradingComplete = () => {
      LogInfo("自动阅卷完成");
      setIsAutoGrading(false);
      message.success("自动阅卷已完成");
    };

    // 自动阅卷进度更新
    const handleAutoGradingProgress = (data: { current: number; total: number }) => {
      LogInfo(`自动阅卷进度: ${data.current}/${data.total}`);
      setAutoGradingProgress(data);
    };

    // 自动阅卷结果更新
    const handleAutoGradingResult = (result: main.GradingResponse) => {
      LogInfo(`自动阅卷结果: 得分=${result.score}`);
      setGradingResults(result);
    };

    // 自动阅卷错误
    const handleAutoGradingError = (error: string) => {
      LogError(`自动阅卷错误: ${error}`);
      // 使用Modal.error创建常驻弹窗提醒
      Modal.error({
        title: '自动阅卷错误',
        content: error,
        okText: '确定',
        maskClosable: false,
        centered: true,
        onOk: () => {
          // 点击确定按钮后的操作
          setIsAutoGrading(false);
        }
      });
      // 出错时自动停止阅卷状态
      setIsAutoGrading(false);
    };

    // 自动阅卷等待API完成
    const handleAutoGradingWaiting = () => {
      LogInfo("等待当前API请求完成");
      setIsWaitingForAPI(true);
      message.info("正在等待当前阅卷完成...");
    };

    // 注册事件监听器
    window.runtime.EventsOn("autoGrading:start", handleAutoGradingStart);
    window.runtime.EventsOn("autoGrading:stop", handleAutoGradingStop);
    window.runtime.EventsOn("autoGrading:complete", handleAutoGradingComplete);
    window.runtime.EventsOn("autoGrading:progress", function () {
      // @ts-ignore
      const data = arguments[0];
      handleAutoGradingProgress(data);
    });
    window.runtime.EventsOn("autoGrading:result", function () {
      // @ts-ignore
      const result = arguments[0];
      handleAutoGradingResult(result);
    });
    window.runtime.EventsOn("autoGrading:error", function () {
      // @ts-ignore
      const error = arguments[0];
      handleAutoGradingError(error);
    });
    window.runtime.EventsOn("autoGrading:fetchimage", function () {
      // @ts-ignore
      const data = arguments[0];
      setImageData(data);
    });
    window.runtime.EventsOn("autoGrading:waiting", handleAutoGradingWaiting);

    // 清理事件监听器
    return () => {
      window.runtime.EventsOff("autoGrading:start");
      window.runtime.EventsOff("autoGrading:stop");
      window.runtime.EventsOff("autoGrading:complete");
      window.runtime.EventsOff("autoGrading:progress");
      window.runtime.EventsOff("autoGrading:result");
      window.runtime.EventsOff("autoGrading:error");
      window.runtime.EventsOff("autoGrading:fetchimage");
      window.runtime.EventsOff("autoGrading:waiting");
    };
  }, []);

  // Fetch URL options from backend using APIService.GetConfig
  useEffect(() => {
    const fetchUrlOptions = async () => {
      try {
        // Import GetConfig from APIService
        const { GetConfig } = await import("../../wailsjs/go/main/APIService");
        const configItems = await GetConfig();

        // Transform the ConfigItems into options format for Select
        // Using ConfigItem.name as label and ConfigItem.id as value
        const options = configItems.map((item) => ({
          label: item.name,
          value: item.id,
        }));

        // 添加自定义选项
        options.push({
          label: "自定义",
          value: "custom"
        });

        if (options.length > 0) {
          setUrlOptions(options);
          // 加载保存的URL
          try {
            const savedUrl = await GetSelectedUrl();
            if (savedUrl) {
              // 检查保存的URL是否在当前的选项中
              const isValidOption = options.some(option => option.value === savedUrl);
              if (isValidOption) {
                // 如果是有效选项，则使用保存的URL
                LogInfo(`使用保存的URL: ${savedUrl}`);
                setSelectedUrl(savedUrl);
              } else if (options[0].value) {
                // 如果不是有效选项，则使用第一个选项
                LogInfo(`保存的URL不在当前选项中，使用第一个选项: ${options[0].value}`);
                setSelectedUrl(options[0].value);
              }
            } else if (!selectedUrl && options[0].value) {
              // 如果没有保存的URL且当前没有选择URL，则使用第一个选项
              setSelectedUrl(options[0].value);
            }
          } catch (error) {
            LogError(`加载保存的URL失败: ${error}`);
            // 出错时，如果当前没有选择URL，则使用第一个选项
            if (!selectedUrl && options[0].value) {
              setSelectedUrl(options[0].value);
            }
          }
        } else {
          LogInfo("No config items found");
        }
      } catch (err) {
        LogError(`Failed to fetch URL options: ${err}`);
      }
    };

    fetchUrlOptions();
  }, []);

  // Fetch subject options from backend using APIService.GetSubjects
  useEffect(() => {
    const fetchSubjectOptions = async () => {
      try {
        // Import GetSubjects from APIService
        const { GetSubjects } = await import("../../wailsjs/go/main/APIService");
        const subjects = await GetSubjects();

        // Transform the Subjects into options format for Select
        // Using Subject.subject_name as label and Subject.subject_id as value
        const options = subjects.map((subject) => ({
          label: subject.subject_name,
          value: subject.subject_id,
        }));

        if (options.length > 0) {
          setSubjectOptions(options);
          LogInfo(`Loaded ${options.length} subjects from backend`);
        } else {
          // 如果没有从后端获取到科目，使用空列表
          setSubjectOptions([]);
          LogInfo("No subjects found from backend, using empty list");
        }
      } catch (err) {
        LogError(`Failed to fetch subject options: ${err}`);
        // 出错时使用空列表
        setSubjectOptions([]);
        LogInfo("Failed to fetch subject options, using empty list");
      }
    };

    fetchSubjectOptions();
  }, []);

  const handleOpenURL = async () => {
    setLoading(true);
    try {
      // 如果选择了自定义选项，打开一个空页面
      if (selectedUrl === "custom") {
        // 打开一个空页面，这里可以根据需要修改
        await OpenURL(selectedUrl);
        // 清除之前保存的区域坐标
        setSelectedAreaCoords(null);
        message.info("已打开自定义页面，请点击右侧的区域选择按钮选择答题卡区域");
      } else {
        // 否则使用常规方式打开URL
        await OpenURL(selectedUrl);
        // 清除之前保存的区域坐标
        setSelectedAreaCoords(null);
      }
    } catch (err) {
      LogError(`无法打开地址: ${err}`);
      const errorMessage = err instanceof Error ? err.message : String(err);
      message.error(`无法打开地址: ${errorMessage}`);
    } finally {
      setLoading(false);
    }
  };

  const handleFetchImage = async () => {
    setLoading(true);
    try {
      if (selectedUrl === "custom") {
        // 当选择自定义URL时，必须选定区域
        if (!selectedAreaCoords) {
          message.warning("请先使用区域选择按钮选择答题卡区域");
          return;
        }
        // 如果有选定的区域坐标，使用区域截图
        const { x, y, width, height } = selectedAreaCoords;
        const dataUrl = await ScreenshotArea(x, y, width, height, 800);
        setImageData(dataUrl);
      } else {
        // 否则使用常规抓图
        const dataUrl = await FetchImage(selectedUrl);
        setImageData(dataUrl);
      }
    } catch (err) {
      LogError(`抓图失败: ${err}`);
      message.error(`抓图失败: 请打开对应阅卷页面`);
    } finally {
      setLoading(false);
    }
  };

  // 处理页面区域选择按钮点击
  const handlePageAreaSelect = () => {
    setIsPageAreaSelectorVisible(true);
  };

  // 处理页面区域选择完成
  const handlePageAreaCapture = (x: number, y: number, width: number, height: number) => {
    LogInfo(`保存页面区域坐标: x=${x}, y=${y}, width=${width}, height=${height}`);
    setSelectedAreaCoords({ x, y, width, height });
    message.success("已保存区域坐标，点击抓图按钮将截取选定区域");
    validateAutoGradingRequirements();
  };

  // 处理页面区域选择取消
  const handlePageAreaSelectCancel = () => {
    setIsPageAreaSelectorVisible(false);
  };

  // 验证自动阅卷所需的条件
  const validateAutoGradingRequirements = () => {
    // 检查是否为自定义URL
    if (selectedUrl !== "custom") {
      // 非自定义URL不需要特殊验证
      setAutoGradingDisabled(false);
      setAutoGradingErrorMsg("");
      return;
    }
    // 对于自定义URL，需要验证区域选择和元素选择
    let errorMsgs = [];
    // 检查区域选择
    if (!selectedAreaCoords) {
      errorMsgs.push("需要设置区域选择");
    }
    // 检查评分标准
    if (!textAreaValue.trim()) {
      errorMsgs.push("需要设置评分标准内容");
    }
    // 检查元素选择
    if (selectedElements.length === 0) {
      errorMsgs.push("需要设置元素选择");
    } else {
      // 检查是否有输入操作元素
      const inputElements = selectedElements.filter(el => el.type === 'input');
      if (inputElements.length === 0) {
        errorMsgs.push("元素选择中需要至少一个输入操作");
      }
    }
    // 更新状态
    if (errorMsgs.length > 0) {
      setAutoGradingDisabled(true);
      setAutoGradingErrorMsg(errorMsgs.join("、"));
    } else {
      setAutoGradingDisabled(false);
      setAutoGradingErrorMsg("");
    }
  };

  // 处理元素选择按钮点击
  const handleElementSelect = () => {
    setIsElementSelectorVisible(true);
  };

  // 处理元素选择完成
  const handleElementSelectSave = (elements: ElementInfo[]) => {
    LogInfo(`保存元素选择，共 ${elements.length} 个元素`);
    setSelectedElements(elements);
    setIsElementSelectorVisible(false);
    validateAutoGradingRequirements();
  };

  // 处理元素选择取消
  const handleElementSelectCancel = () => {
    setIsElementSelectorVisible(false);
  };

  const handleLogout = () => {
    logout();
  };

  // 处理自动阅卷按钮点击
  const handleAutoGrading = async () => {
    // 如果余额不足，不允许启动自动阅卷
    if (!isBalanceSufficient && !isAutoGrading) {
      message.error("账户余额不足，请充值后再试");
      return;
    }

    // 如果已经在自动阅卷中，则停止
    if (isAutoGrading) {
      LogInfo("用户请求停止自动阅卷");
      try {
        // 根据URL类型调用不同的停止函数
        if (selectedUrl === "custom") {
          // 对于自定义URL，使用自定义自动阅卷函数停止
          // 传递count=0表示停止
          await AutoGradingLoopCustom(
            selectedSubject,
            textAreaValue,
            0,
            selectedAreaCoords || { x: 0, y: 0, width: 0, height: 0 },
            JSON.stringify(selectedElements)
          );
        } else {
          // 对于预设URL，使用原有的自动阅卷函数停止
          await AutoGradingLoop(selectedUrl, selectedSubject, textAreaValue, 0);
        }
        // 重置状态
        setIsAutoGrading(false);
        setIsWaitingForAPI(false);
        setLoading(false);
      } catch (err) {
        LogError(`停止自动阅卷失败: ${err}`);
        message.error(`停止自动阅卷失败: ${err}`);
        // 即使出错也重置状态
        setIsAutoGrading(false);
        setIsWaitingForAPI(false);
        setLoading(false);
      }
      return;
    }

    // 检查参数
    if (!selectedUrl) {
      message.warning("请选择阅卷网站");
      return;
    }

    if (!textAreaValue.trim()) {
      message.warning("请设置评分标准");
      return;
    }

    if (!paperCount || paperCount <= 0) {
      message.warning("试卷数量必须大于0");
      return;
    }

    // 对于自定义URL，进行额外验证
    if (selectedUrl === "custom") {
      // 验证区域选择
      if (!selectedAreaCoords) {
        message.warning("请先使用区域选择按钮选择答题区域");
        return;
      }

      // 验证元素选择
      if (selectedElements.length === 0) {
        message.warning("请先使用元素选择按钮设置提交评分的元素");
        return;
      }

      // 验证是否有输入操作元素
      const inputElements = selectedElements.filter(el => el.type === 'input');
      if (inputElements.length === 0) {
        message.warning("元素选择中需要至少一个输入操作元素");
        return;
      }
    }

    LogInfo(`开始自动阅卷，试卷数量: ${paperCount}`);
    setLoading(true);

    try {
      // 对于自定义URL，使用自定义自动阅卷函数
      if (selectedUrl === "custom") {
        LogInfo("使用自定义自动阅卷模式");

        // 确保区域坐标和元素选择都已设置
        if (!selectedAreaCoords) {
          throw new Error("未设置区域选择");
        }

        if (selectedElements.length === 0) {
          throw new Error("未设置元素选择");
        }

        // 将元素选择转换为JSON字符串
        const elementsJson = JSON.stringify(selectedElements);

        // 调用自定义自动阅卷函数
        await AutoGradingLoopCustom(
          selectedSubject,
          textAreaValue,
          paperCount,
          selectedAreaCoords,
          elementsJson
        );
      } else {
        // 对于预设URL，使用原有的自动阅卷函数
        LogInfo("使用预设自动阅卷模式");
        await AutoGradingLoop(selectedUrl, selectedSubject, textAreaValue, paperCount);
      }
    } catch (err) {
      LogError(`开始自动阅卷失败: ${err}`);
      message.error(`开始自动阅卷失败: ${err}`);
      setIsAutoGrading(false);
    } finally {
      setLoading(false);
    }
  };

  const handleSubmitGrade = async () => {
    LogInfo("handleSubmitGrade called");
    // 检查：如果使用 AI 评分，则必须有评分结果
    if (scoreInputDisabled && (!gradingResults || gradingResults.score === undefined)) {
      message.warning("请先使用 AI 评分后再提交");
      return;
    }

    // 检查：如果使用自定义评分，则必须输入了分数
    if (!scoreInputDisabled && customScore === null) {
      message.warning("请输入自定义分数");
      return;
    }

    setLoading(true);
    // 添加提交评分计时
    const startTime = performance.now();
    try {
      // 使用自定义分数或评分结果中的分数
      const scoreToSubmit = !scoreInputDisabled && customScore !== null ? customScore : gradingResults!.score;
      // 如果是自定义URL且有选择的元素，则使用元素操作
      if (selectedUrl === "custom" && selectedElements.length > 0) {
        LogInfo(`使用元素操作提交评分，共 ${selectedElements.length} 个元素`);
        // 查找输入操作元素
        const elementsToSubmit = [...selectedElements];
        const inputElementIndex = elementsToSubmit.findIndex(el => el.type === 'input');
        if (inputElementIndex === -1) {
          message.warning("请至少添加一个输入操作元素");
          return;
        }
        // 更新输入操作元素的值为分数
        elementsToSubmit[inputElementIndex] = {
          ...elementsToSubmit[inputElementIndex],
          value: scoreToSubmit.toString()
        };
        // 执行元素操作
        await ExecuteElementOperations(JSON.stringify(elementsToSubmit));
        message.success("评分已提交");
      } else if (selectedUrl === "custom") {
        // 如果是自定义URL但没有选择元素，则提示用户选择元素
        message.warning("请先使用元素选择按钮设置元素操作");
      } else {
        // 使用常规方式提交评分
        await FillGrade(selectedUrl, scoreToSubmit.toString());
        message.success("评分已提交");
      }
    } catch (err) {
      LogError(`提交评分失败: ${err}`);
      const errorMessage = err instanceof Error ? err.message : String(err);
      message.error(`提交评分失败: ${errorMessage}`);
    } finally {
      const endTime = performance.now();
      const totalDuration = endTime - startTime;
      LogInfo(`提交评分总耗时: ${totalDuration.toFixed(2)}ms`);
      setLoading(false);
    }
  };

  return (
    <Layout className="layout">
      <Header className="header">
        <div className="logo">
          <img src={LogoImage} alt="Logo" style={{ height: 32, marginRight: 8 }} />
          山竹阅卷
        </div>
        <div className="user-info">
          <span>
            <UserOutlined style={{ marginRight: 5, color: '#52c41a' }} />
            {username}
          </span>
          <span onDoubleClick={() => {
            refreshBalance();
            message.info("正在刷新积分余额...");
            LogInfo("用户双击积分图标，刷新余额");
          }}>
            <WalletOutlined style={{ marginRight: 5, color: '#52c41a' }} />
            积分: {balance !== null ? balance : "加载中..."}
          </span>
          <Button type="link" onClick={handleLogout} icon={<LogoutOutlined />}>
          </Button>
        </div>
      </Header>
      <Layout className="sub-layout">
        <Content className="content">
          <div className="content-container">
            <div className="url-input-container">
              <Select
                value={selectedUrl}
                onChange={(value) => {
                  setSelectedUrl(value);
                  // 当URL变化时重新验证
                  setTimeout(validateAutoGradingRequirements, 0);
                  // 记录用户选择的URL
                  LogInfo(`用户选择URL: ${value}`);
                }}
                size="middle"
                style={{ width: 110, marginRight: 10 }}
                showSearch
                disabled={isAutoGrading || !isBalanceSufficient}
                optionFilterProp="label"
                filterOption={(input, option) => (option?.label ?? "").toLowerCase().includes(input.toLowerCase())}
                options={urlOptions}
              />
              <Button
                type="primary"
                onClick={handleOpenURL}
                icon={<GlobalOutlined />}
                disabled={isAutoGrading || !isBalanceSufficient}
              >
                打开
              </Button>
            </div>

            <div className="screenshot-container">
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <span style={{ fontWeight: "bold" }}>答题卡</span>
                {selectedAreaCoords && (
                  <div style={{ fontSize: 12, color: '#888' }}>
                    已选择区域: {selectedAreaCoords.width}x{selectedAreaCoords.height}
                  </div>
                )}
              </div>

              {imageData ? <Image src={imageData} alt="Screenshot" style={{ maxWidth: "100%" }} /> : <Empty />}

              <div className="student-answer-container" style={{ marginTop: 5 }}>
                <span style={{ fontWeight: "bold" }}>学生答案</span>
                {gradingResults && gradingResults.student_answer ? (
                  <div
                    style={{
                      border: "1px solid #d9d9d9",
                      borderRadius: "6px",
                      padding: "10px",
                      height: "165px",
                      marginBottom: "5px",
                      overflow: "auto",
                      backgroundColor: "#f5f5f5",
                    }}
                  >
                    {gradingResults.student_answer}
                  </div>
                ) : (
                  <Empty
                    style={{
                      padding: "10px",
                      height: "165px",
                      marginBottom: "10px",
                    }}
                  />
                )}
              </div>

              <div style={{ display: "flex", flexDirection: "column", alignItems: "center", marginTop: 5, marginBottom: 5 }}>
                <div style={{ marginBottom: 10, display: "flex", alignItems: "center" }}>
                  <span style={{ marginRight: 10, fontWeight: "bold" }}>试卷数量:</span>
                  <InputNumber
                    min={1}
                    max={1000}
                    style={{ width: 100 }}
                    placeholder="试卷数量"
                    value={paperCount}
                    onChange={(value) => setPaperCount(value as number)}
                    disabled={isAutoGrading}
                  />
                </div>
                <div style={{ display: "flex", flexDirection: "column", alignItems: "center" }}>
                  <Button
                    className="auto-grade-button geek-button"
                    type={isAutoGrading ? "primary" : undefined}
                    danger={isAutoGrading}
                    size="large"
                    icon={<ThunderboltOutlined />}
                    onClick={handleAutoGrading}
                    disabled={isWaitingForAPI || !isBalanceSufficient || (selectedUrl === "custom" && autoGradingDisabled)}
                  >
                    {isAutoGrading ? (isWaitingForAPI ? "等待完成..." : "停止阅卷") : "AI 自动阅卷"}
                  </Button>
                  {selectedUrl === "custom" && autoGradingErrorMsg && (
                    <div style={{ color: "red", fontSize: "12px", marginTop: "5px", textAlign: "center" }}>
                      {autoGradingErrorMsg}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </Content>
        <Sider className="sider" width={350}>
          <div className="sider-content">
            <div style={{ display: 'flex', marginBottom: 10 }}>
              <Button
                type="primary"
                icon={<CameraOutlined />}
                onClick={handleFetchImage}
                style={{ flex: 1, marginRight: 5 }}
                disabled={isAutoGrading || !isBalanceSufficient}
              >
                抓图
              </Button>
              {selectedUrl === "custom" && (
                <Button
                  type="primary"
                  icon={<ScissorOutlined />}
                  onClick={handlePageAreaSelect}
                  style={{ width: 120 }}
                  disabled={isAutoGrading || !isBalanceSufficient}
                >
                  区域选择
                </Button>
              )}
            </div>
            <div
              style={{
                display: "flex",
                alignItems: "center",
                justifyContent: "space-between",
              }}
            >
              <h3 style={{ margin: 0, marginRight: 10 }}>评分标准</h3>
              <Button
                type="primary"
                size="small"
                icon={<EditOutlined />}
                onClick={() => setGradingCriteriaDialogOpen(true)}
                style={{ marginRight: "auto" }}
                disabled={isAutoGrading || !isBalanceSufficient}
              >
                编辑
              </Button>
              <Select
                value={selectedSubject}
                onChange={(value) => {
                  setSelectedSubject(value);
                  // 当科目变化时，使用延迟保存到配置文件
                  debouncedSaveGradingCriteria(textAreaValue, value);
                }}
                style={{ width: 120 }}
                options={subjectOptions}
                disabled={isAutoGrading || !isBalanceSufficient}
              />
            </div>
            <div className="sider-textarea">
              <div
                style={{
                  border: "1px solid #d9d9d9",
                  borderRadius: "6px",
                  padding: "10px",
                  height: "120px",
                  marginBottom: "10px",
                  overflow: "auto",
                  backgroundColor: "#f5f5f5",
                }}
              >
                <div style={{ whiteSpace: "pre-wrap" }}>{textAreaValue || "暂无评分标准，请点击编辑按钮设置"}</div>
              </div>
              <Button
                type="primary"
                onClick={handleTextAreaSubmit}
                icon={<SendOutlined />}
                disabled={isAutoGrading || !isBalanceSufficient}
              >
                测试阅卷
              </Button>
              <div>
                <div style={{ display: "flex", alignItems: "center", marginBottom: 10 }}>
                  <h4 style={{ marginRight: 10 }}>评分结果</h4>
                  <InputNumber
                    min={0}
                    max={100}
                    placeholder="得分："
                    value={
                      !scoreInputDisabled && customScore !== null
                        ? customScore
                        : gradingResults && gradingResults.score !== undefined
                        ? gradingResults.score
                        : 0
                    }
                    onChange={(value) => setCustomScore(value as number)}
                    disabled={scoreInputDisabled || isAutoGrading}
                    style={{ width: 110 }}
                  />
                  <div style={{ display: "flex", flexDirection: "column", alignItems: "center" }}>
                    <Button
                      onClick={toggleScoreInput}
                      type="primary"
                      size="small"
                      style={{ width: 75, margin: 10, height: 25 }}
                      disabled={isAutoGrading || !isBalanceSufficient}
                    >
                      {scoreInputDisabled ? "修改分数" : "还原"}
                    </Button>
                    <span style={{ fontSize: "10px", color: "#888", marginTop: "-8px" }}>点击可自定义分数</span>
                  </div>
                </div>
                <Card styles={{ body: { whiteSpace: "pre-wrap", minHeight: "130px", maxHeight: "130px", overflow: "auto" } }}>
                  {gradingResults && gradingResults.grading_details ? gradingResults.grading_details : "暂无评分详情"}
                </Card>
              </div>
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <Button
                  type="primary"
                  icon={<UploadOutlined />}
                  onClick={handleSubmitGrade}
                  style={{ marginRight: 5 }}
                  disabled={isAutoGrading || !isBalanceSufficient}
                >
                  提交评分
                </Button>

                {selectedUrl === "custom" && (
                  <Button
                    type="primary"
                    icon={<AimOutlined />}
                    onClick={handleElementSelect}
                    disabled={isAutoGrading || !isBalanceSufficient}
                  >
                    元素选择
                  </Button>
                )}
              </div>
            </div>
          </div>
        </Sider>
      </Layout>
      <Footer className="footer">
        <div className="footer-container">
          <div className="footer-left">
            {isAutoGrading && (
              <div className="footer-progress">
                <Spin spinning={isAutoGrading} size="small"></Spin>
                <div className="paper-progress">
                  阅卷进度：{autoGradingProgress.current}/{autoGradingProgress.total}份试卷
                </div>
              </div>
            )}
          </div>
          <div className="footer-copyright">Copyright © {new Date().getFullYear()} ShanZhuLab</div>
          <div className="footer-right"></div>
        </div>
      </Footer>
      <GradingCriteriaDialog
        open={gradingCriteriaDialogOpen}
        onCancel={() => setGradingCriteriaDialogOpen(false)}
        onSuccess={(criteria) => {
          setTextAreaValue(criteria);
          setGradingCriteriaDialogOpen(false);
          validateAutoGradingRequirements();
          // 重新加载结构化数据
          GetGradingCriteriaStruct().then(structuredData => {
            if (structuredData) {
              setStructuredCriteria({
                defaultCriteria: structuredData.default_criteria || "",
                scoringPoints: structuredData.scoring_points || "",
                deductionPoints: structuredData.deduction_points || "",
                totalScore: structuredData.total_score || 0,
              });
            }
          }).catch(err => {
            LogError(`重新加载结构化评分标准失败: ${err}`);
          });
        }}
        initialCriteria={textAreaValue}
        subject={selectedSubject}
        structuredCriteria={structuredCriteria}
      />
      <FloatButton
        icon={isAlwaysOnTop ? <PushpinFilled /> : <PushpinOutlined />}
        type={isAlwaysOnTop ? "primary" : "default"}
        style={{ left: 15, bottom: 35 }} // 定位到左下角
        onClick={toggleAlwaysOnTop}
        tooltip={isAlwaysOnTop ? "取消置顶" : "窗口置顶"}
      />
      <GradingRecordsDialog visible={gradingRecordsDialogVisible} onClose={() => setGradingRecordsDialogVisible(false)} />
      <PageAreaSelector
        visible={isPageAreaSelectorVisible}
        onClose={handlePageAreaSelectCancel}
        onCapture={handlePageAreaCapture}
      />
      <ElementSelector
        visible={isElementSelectorVisible}
        onClose={handleElementSelectCancel}
        onSave={handleElementSelectSave}
      />
    </Layout>
  );
};

export default Home;